# 中英文数据集分离工具

## 概述

这个工具用于将混合的中英文JSONL数据集按语言分离为两个独立的文件。

## 文件说明

- `separate_languages.py` - 主要的分离脚本
- `verify_separation.py` - 验证分离结果的脚本
- `001_00001.jsonl` - 原始数据集文件
- `chinese_data.jsonl` - 分离出的中文数据
- `english_data.jsonl` - 分离出的英文数据

## 分离规则

1. **中文检测**: 使用Unicode范围 `[\u4e00-\u9fff]` 检测中文字符
2. **分类逻辑**: 
   - 如果中文字符比例超过5%，归类为中文
   - 如果包含中文字符且总字符数少于100个，归类为中文
   - 其他情况归类为英文
3. **混合内容处理**: 如果一个条目既有中文又有英文，按要求归类为中文

## 分离结果

### 原始数据统计
- **总条目数**: 25,492

### 分离后统计
- **中文条目数**: 3,893 (15.27%)
- **英文条目数**: 21,599 (84.73%)

### 分类准确率
- **中文文件准确率**: 100.00% (所有条目都包含中文)
- **英文文件准确率**: 99.75% (仅有0.25%的混合条目)

## 使用方法

### 1. 运行分离脚本
```bash
python separate_languages.py
```

### 2. 验证分离结果
```bash
python verify_separation.py
```

## 脚本特性

### separate_languages.py
- 支持大文件处理（显示进度）
- 错误处理和日志记录
- 自动创建输出目录
- 保持原始JSON格式
- 支持UTF-8编码

### verify_separation.py
- 详细的统计分析
- 分类准确率计算
- 样本数据预览
- 语言分布分析

## 技术细节

### 中文字符检测
```python
def contains_chinese(text: str) -> bool:
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    return bool(chinese_pattern.search(text))
```

### 中文字符比例计算
```python
def get_chinese_char_ratio(text: str) -> float:
    text_no_space = re.sub(r'\s', '', text)
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text_no_space)
    return len(chinese_chars) / len(text_no_space)
```

## 输出文件格式

输出文件保持原始JSONL格式：
```json
{"text": "对话内容...", "metadata": {"loss": 0.123, "toxicity": 0.001}}
```

## 注意事项

1. 脚本假设输入文件为UTF-8编码
2. 每行必须是有效的JSON格式
3. 分离过程中会跳过无效的JSON行并记录错误
4. 输出文件会覆盖同名的现有文件

## 系统要求

- Python 3.6+
- 标准库模块：json, re, os, typing

## 许可证

此工具仅供学习和研究使用。
