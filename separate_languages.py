#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中英文数据集分离脚本
将JSONL格式的数据集按语言分离为中文和英文两个文件
如果一个条目中既有中文又有英文，则归类为中文条目
"""

import json
import re
import os
from typing import Dict, Any, Tu<PERSON>

def contains_chinese(text: str) -> bool:
    """
    检查文本是否包含中文字符

    Args:
        text: 要检查的文本

    Returns:
        bool: 如果包含中文字符返回True，否则返回False
    """
    # 中文字符的Unicode范围（常用汉字）
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    return bool(chinese_pattern.search(text))

def get_chinese_char_ratio(text: str) -> float:
    """
    计算文本中中文字符的比例

    Args:
        text: 要检查的文本

    Returns:
        float: 中文字符比例 (0.0 - 1.0)
    """
    if not text:
        return 0.0

    # 移除空白字符
    text_no_space = re.sub(r'\s', '', text)
    if not text_no_space:
        return 0.0

    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text_no_space)
    return len(chinese_chars) / len(text_no_space)

def classify_entry(entry: Dict[str, Any]) -> str:
    """
    对数据条目进行语言分类

    Args:
        entry: JSON数据条目

    Returns:
        str: 'chinese' 或 'english'
    """
    text_content = entry.get('text', '')

    # 计算中文字符比例
    chinese_ratio = get_chinese_char_ratio(text_content)

    # 如果中文字符比例超过5%，或者包含中文字符且总字符数较少，归类为中文
    if chinese_ratio > 0.05 or (contains_chinese(text_content) and len(text_content.replace(' ', '')) < 100):
        return 'chinese'
    else:
        return 'english'

def separate_languages(input_file: str, output_dir: str = '.') -> Tuple[int, int, int]:
    """
    分离中英文数据集
    
    Args:
        input_file: 输入的JSONL文件路径
        output_dir: 输出目录，默认为当前目录
        
    Returns:
        Tuple[int, int, int]: (总条目数, 中文条目数, 英文条目数)
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 输出文件路径
    chinese_file = os.path.join(output_dir, 'chinese_data.jsonl')
    english_file = os.path.join(output_dir, 'english_data.jsonl')
    
    total_count = 0
    chinese_count = 0
    english_count = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(chinese_file, 'w', encoding='utf-8') as chinese_out, \
             open(english_file, 'w', encoding='utf-8') as english_out:
            
            for line_num, line in enumerate(infile, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 解析JSON
                    entry = json.loads(line)
                    total_count += 1
                    
                    # 分类
                    language = classify_entry(entry)
                    
                    if language == 'chinese':
                        chinese_out.write(json.dumps(entry, ensure_ascii=False) + '\n')
                        chinese_count += 1
                    else:
                        english_out.write(json.dumps(entry, ensure_ascii=False) + '\n')
                        english_count += 1
                    
                    # 每处理1000条记录打印一次进度
                    if total_count % 1000 == 0:
                        print(f"已处理 {total_count} 条记录...")
                        
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    continue
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return 0, 0, 0
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return 0, 0, 0
    
    return total_count, chinese_count, english_count

def test_classification():
    """测试分类函数"""
    test_cases = [
        {"text": "Human:\nCreate a Python script snippet that Updates Low Bathing routine", "expected": "english"},
        {"text": "Human:\n根据给定条件进行推理。\n爱丽丝比鲍勃年长4岁。", "expected": "chinese"},
        {"text": "Human:\nWhat is the best thing someone's ever said to you?", "expected": "english"},
        {"text": "Human:\n鉴别两种风格迥异的艺术品，分析其不同之处。", "expected": "chinese"},
    ]

    print("测试分类逻辑:")
    for i, case in enumerate(test_cases):
        result = classify_entry(case)
        status = "✓" if result == case["expected"] else "✗"
        chinese_ratio = get_chinese_char_ratio(case["text"])
        print(f"{status} 测试 {i+1}: {result} (中文比例: {chinese_ratio:.3f}) - 预期: {case['expected']}")
    print()

def main():
    """主函数"""
    input_file = '001_00001.jsonl'

    # 先测试分类逻辑
    test_classification()

    print("开始分离中英文数据集...")
    print(f"输入文件: {input_file}")

    total, chinese, english = separate_languages(input_file)

    print("\n分离完成!")
    print(f"总条目数: {total}")
    print(f"中文条目数: {chinese}")
    print(f"英文条目数: {english}")
    if total > 0:
        print(f"中文比例: {chinese/total*100:.2f}%")
        print(f"英文比例: {english/total*100:.2f}%")

    print(f"\n输出文件:")
    print(f"中文数据: chinese_data.jsonl")
    print(f"英文数据: english_data.jsonl")

if __name__ == "__main__":
    main()
